# 基本功8：搭团队|找合伙人

## 引言

创业是一场修行，也是一场组队打怪。再牛的个人英雄，也无法单枪匹马打赢一场战争。找到志同道合的合伙人，搭建一个能力互补、价值观一致的团队，是创业成功的关键。

## 训练任务

学会如何找到合适的合伙人，搭建高效的创业团队，并有效管理团队。

训练工具：

什么是精益呢？核心就是小步快跑，快速试错。尽量避免在一开始，就做一个大而全的产品，一次性就把所有资源都赌上，企图把所有验证工作一次性都做完。

关于这种创业方法论，我们推荐三本书：第一本是《精益创业》，在创业圈已广为人知；第二本是《精益创业实战》，里面有很多实操建议；第三本是《精益商业思维》，作者是迅雷创始人程浩。

程浩是国内精益话题最好的分享者，作为一堂的荣誉校长，程浩和我们一起打磨了一套精益的实操版课程。面对一些需求不确定，高风险的业务，可以用精益五步法”快速提升业务测试效率。

训练任务：避免大而全的产品开发，面对高风险的创业方向，极力避免憋大招的情况，用小步快跑的实验，来不断测试，不断试错转型，最终快速跑通商业模式。

训练工具：


<html><body><table><tr><td>工具</td><td>类型</td><td>训练目的</td></tr><tr><td>精益验证模型</td><td>方法模型（Framework)</td><td>掌握精益验证的5个步骤</td></tr><tr><td>MVP设计武器库</td><td>方法模型（Framework）</td><td>掌握低成本的验证方法</td></tr></table></body></html>

这个模块相对比较硬，建议快速熟悉方法论以后，认真复盘几轮自己的业务或者身边朋友的业务，然后回答一个问题：如果现在重新操盘测试，你会怎么做？多做几次案例代入的创业推演”，可以帮你更好地掌握精益这套创业方法论。

# 88以太一堂·内部训练手册

# 模型：精益验证模型

很多人可能对于精益的概念很熟悉，但不知道怎么落地。对于这个问题，我们总结了精益创业落地的五个关键环节，抓住这几个环节，你的精益工作就能算合格了。

这五个关键环节分别是：

1.快速探索需求：初步验证一下用户的需求到底痛不痛。
2.验证用户方案：设计一个MVP来直接测试方案可行性。
3.做好数据判断：拿到信息以后，如何分析数据。
4.决定转型还是坚持：如果验证不顺利想转型，你可以有哪些转型的思路。
5.加速增长扩张：如果模型跑通，如何加速扩张。

# 第一步：快速探索需求

这里有三个常见方法：第一个是痛点分析，第二个是团队头脑风暴，第三个是寻找核心用户。谁是核心用户呢？“谁最痛，谁就是核心用户”。

# 第二步：验证用户方案

MVP 从成本最低到最高的演化过程，一共有五条关键策略，在后面的“MVP设计武器库框架”里展开进行了介绍。在这里，我们先分享妨碍创业者做好MVP的四个常见心态。

心态1：完美主义倾向，总想把最全、最好的产品或者版本给用户。
心态2：担心被大公司或竞争对手抄袭。
心态3：担心品牌风险，一般来说只有大公司才会担心推出一款MVP，因为不成熟甚至比较差，会拖累公司的品牌。
心态4：担心MVP不成功，导致团队不稳定。

# 第三步：做好数据判断

这里有四个常用工具。

第一个工具，是转化漏斗分析。这个对于电商特别常用。
第二个工具，是北极星指标和海盗模型。这是增长黑客理论里面，最重要的两个指标工具。
第三个工具，叫CohortAnalysis，有人翻译为同期群分析，我们把它称为切片分析，

CohortAnalysis做留存率分析特别方便。

第四个数据分析工具，是A/B测试。一个产品设计可能有两套方案，究竟A好还是B好，谁也说不清楚，怎么办？此时可以用A/B测试。

强调下，这些数据工具都只是工具，是术”层面的东西，肯后的核心还是你要懂业务模型，以及行业里的Benchmark。

# 第四步：决定转型还是坚持

如果你在第三步，判断商业模式成立，那恭喜你，可以进入第五步，开始复制扩张，获取更多的用户。

但现实情况是，有很大的概率，你的前几版方案都是不成立的，你需要不断试错、调整和转型，才能逐步接近最终那个成立的模式。

创业户最常见的7种转型：

第一种：精简型，做小而美；

第二种：扩充型，做全套；

第三种：ToC转ToB；

第四种：自营转平台；

第五种：收费转免费；

第六种：代理转直销或者直销转代理；

第七种：技术方案转型

# 第五步：加速增长扩张

增长是个不亚于精益的体系，通常《精益创业》的书都对增长讲得很浅，因为不同项目的增长方式差异极大。这里不展开了，推荐几个话题：增长黑客、销售铁军、私域流量、投放买量、病毒营销。

希望你能清楚精益的执行框架，如果从0启动一个业务，你该怎么一步步推进验证。先探索需求找痛点，再设计MVP验证，然后做好数据判断，如果没跑通怎么调整或转型，最后如果跑通了，开始增长扩张。

# 90|以太一堂·内部训练手册

# 模型：MVP 设计武器库

很多创业者虽然看了很多精益方面的书，自以为掌握了其精髓，但是落地实操后，还是老样子，开发一个大而全的产品。

发生这种情况，很多时候，还是创业者没懂怎么设计MVP。客观来说，能否设计一个最低成本、最便宜甚至可以说最聪明的MVP，来验证自己的创业方案，是很考验大家的功力的。

为了解决这个问题，我们提供了一个原则，设计MVP实验，在能验证假设的情况下，要越快越好、越简单越好，越直接越好。在这个原则下，我们提供MVP设计武器库框架（见下图），这张图总共涵盖了5个策略，从左往右，开发成本越来越高。

当我们面临MVP设计时，都要反复问自己一个问题：是不是可以先选择更左侧的方式，如果真的跑通了，再切换右边的方法继续测试，随着确定性变高，投入资源也越来越高，这才是一个真正优秀的创业路径。

# MVP设计武器库：

![](images/f3001eff9ac1eaf3ac922cb41318c5cda23f09b17bf5652f8e72dce88dc4270c.jpg)
从左到右的MVP成本越来越高

这个MVP设计策略的武器库，是一堂教研组收集了国内国外能找到的所有MVP精彩案例，再进行分门别类并寻找规律，最后研制而成的。

大家未来要是遇到MVP设计的难题，可以从武器库里的几十个案例选出跟自己项目相近的案例，匹配后，进行后续的测试。

#打造你的增长优势#

# 第三篇打造优势

业务早期讲究糙快猛，依赖几个人的单打独斗而这个阶段你要把你的业务，变成一个团队，一个队伍，用一个可持续的模型，构建足够强大的护城河，持续拉动公司往前走

当你有了稳定的业务模型、有了收入、有了团队，如何持续做增长、拿资源、建壁垒呢？这一篇给你一些训练工具。

Once you've established c stable business mcdel,begun generating revenue,and built a team,how do you maintain growth,obtain resources and build barriers?This article offers some practical tools to help

# 92以太一堂·内部训练手册

# 基本功9：建飞轮|增长飞轮

提到增长飞轮，最经典的就是亚马逊增长飞轮

# 最佳实践： 经典亚马逊增长飞轮

一堂重绘版·2022，《重新理解“增长飞轮”》

![](images/e5f091e21faf5d7f167df8295651215243437633bb65bff45044631539fa9fa5.jpg)

贝索斯把亚马逊的增长策略，画成了一个圈，形成了一个环，把这个叫做飞轮。这个飞轮20多年，一直在滚动、在迭代、造就了一个亚马逊的巨大商业帝国。

其实增长飞轮”这个模型，已经有很多优秀的友商拆解过了。很多同行都在讲一些大公司的案例，更多侧重思维”。但是我们更希望给大家一套可以拿来就用"的实操方法。

创业任务：有能力开始构建两类飞轮：简单的小飞轮，复杂的公司战略大飞轮。

训练工具：


<html><body><table><tr><td>工具</td><td>类型</td><td>训练目的</td></tr><tr><td>增长飞轮</td><td>方法模型（Framework）</td><td>理解飞轮效应构建飞轮思维</td></tr><tr><td>小飞轮三段论</td><td>方法模型（Framework）</td><td>学会构建自己的小飞轮</td></tr><tr><td>飞轮构建四步法</td><td>方法模型（Framework）</td><td>试着构建公司的战略飞轮</td></tr></table></body></html>

# 模型：增长飞轮

关于增长飞轮，同行讲了很多很深度的分析，这里来讲讲一堂的理解。

增长飞轮的本质，是把一堆增长要素的因果关系，串起来，形成一个增强回路”。

举个例子帮你理解。随着增长，你的业务可能会有很多要素发生变化：有更多的用户（C）更多的客户（B）、更多的日活、更多的毛利、更快的配送速度、更便宜的价格等等。

这些要素之间有些什么内在联系呢？你会如何理解这些要素呢？常见的有三种思考方式

# 你是如何理解关键要素的？

![](images/ba006026726cd1fa03fa686105f5073794a588db0554233e08501fe87b.jpg)

第一类：散点思考。对于业务难题，全是一个个独立问题，哪儿出问题，去哪儿救火。
只看局部，没有因果，没有整体。

第二类：申行思考。常常可以把业务拆解成一个漏斗或者公式，能够站在体系的角度思考问题。

第三类：飞轮思考。尝试把各个要素形成一个闭环，找到因果关系，互为促进，持续加强，形成一个增强回路""

这三种思考方式一对比，就不难发现其中的区别以及飞轮的优势。

# 94|以太一堂·内部训练手册

# 模型：小飞轮三段论

拆解像亚马逊那样的商业战略飞轮很难，我们可以先试着拆解一些简单的飞轮：

1.关于个人学习（如提升认知段位）

2.关于某一项能力提升（如提升拍短视频&学习投资）

3.持续做某一个小的产品/项目（如写一本书&讲拆书会）

这类飞轮比较简单，不涉及到公司的经营，不涉及到商业战略，不涉及到用户价值洞察，不涉及到多个业务板块，为了方便记忆，我们把这类简单的飞轮叫做“小飞轮”。

具体如何构建简单的小飞轮呢，重点是要围绕你的核心卡点”进行。为此我们总结了小飞轮三段论：找卡点，加环节，做闭环。

用一个简单的例子带你理解。比如，有很多人平时持续写作，非常辛苦，大致的流程是：想选题、写文章、发出去。

第一步，找卡点。你要分析，你最难受的环节是什么，你卡在哪个环节：缺选题和素材、缺成就感、缺金钱回报。

第二步，加环节。找到你的卡点之后，针对你的卡点，有哪些环节可以帮你解决这个难题。比如你卡在没选题素材。调研后你发现很多畅销书作者或者持续写作选手，会用用户投票选题、引导评论区留言、用户采访等等方法收集素材。

第三步，做闭环。发现这些环节后，你就可以试着写出你的飞轮了。更多选题-更好写作-发布-鼓励用户参与互动-分析用户案例-更多选题。
当然飞轮不是想出来的，需要大胆假设，小心求证”，一旦这个闭环真正形成，你的工作就会越来越简单。

# 模型：飞轮构建四步法

关于增长飞轮，市场上都在讲Why，都在讲飞轮有多厉害，有多神奇，但是讲How的极少，那么如何真正在自己的业务上思考和构建飞轮呢？

我们结合吉姆·柯林斯的《飞轮效应》这本书和一堂的研究，做了简化，总结一个简版的增长飞轮构建方法：列要素、找因果、测闭环、狂拉动。

第一步列要素：找出飞轮的要素。需要列出来一些飞轮的要素，常见的要素包含：

·用户：用户，客户，更多APP下载，更多Web 流量，更多私域粉丝·价值：更好体验，多、快、好、省、更好的课程、更好的AI算法。·商业：更低成本结构，更高毛利，更多收入，更多利润，更高效率。·壁垒：品牌/声誉，更大规模，更强技术团队，更强粘性。

第二步找因果：整理构件逻辑关系。需要找到所有关键的因果链，作为你的假设，要勇敢提出来。

常见话术其实就是两条：这个会拉动什么？（看下一步）这个依赖什么？（看上一步）